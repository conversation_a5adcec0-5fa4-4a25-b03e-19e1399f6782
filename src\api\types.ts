// 通用响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
  success?: boolean
}

// 政策信息搜索参数
export interface PolicyInfoSearchParams extends BaseSearchParams {
  currentPage: number
  pageSize: number
  policyName: string //政策名称（文本框）
  publishStartDate: string //发布日期（开始，区间日期选择框），yyyy-MM-dd
  publishEndDate: string //发布日期（结束，区间日期选择框），yyyy-MM-dd
  policyLevel: string //政策层级（下拉，字典zccj）
  applicableRegion: string //适用地区（下拉，适用地区字典）
  policyType: string //政策类型（下拉，字典zclx）
  policyNumber: string //政策编号（文本框）
  controlledPollutants: string //涉及污染物（下拉，字典sjwrw）
  dataStatus: string //数据状态（下拉，状态下拉）
}

// 分页请求参数
export interface PageParams {
  page?: number
  pageSize?: number
  current?: number
  size?: number
}

// 分页响应数据
export interface PageResult<T = any> {
  list: T[]
  total: number
  current: number
  size: number
  pages?: number
  records?: T[]
}

// 用户相关类型
export interface User {
  id: string | number
  username: string
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  roles?: string[]
  permissions?: string[]
  createTime?: string
  updateTime?: string
}

// 登录请求参数
export interface LoginParams {
  username: string
  password: string
  captcha?: string
  rememberMe?: boolean
}

// 登录响应数据
export interface LoginResult {
  token: string
  refreshToken?: string
  userInfo: User
  expires?: number
}

// 知识库案例类型
export interface ExcellentCase {
  id: string | number
  title: string
  category: string
  region: string
  effect: string
  content?: string
  attachments?: string[]
  tags?: string[]
  status: 'draft' | 'published' | 'archived'
  createTime: string
  updateTime?: string
  creator: string
  viewCount?: number
}

// 政策文件信息
export interface PolicyFile {
  fileId: string
  fileName: string
  filePath?: string
  tableName?: string
  tableId?: string
  fileType?: string
  isActive?: boolean
  url: string
}

// 政策信息类型
export interface PolicyInfo {
  policyId?: string
  policyName: string
  publishDepartment: string
  publishDate: string
  implementationDate: string
  expiryDate?: string
  policyLevel: string
  applicableRegion: string[]
  policyType: string
  policyNumber: string
  coreTheme: string
  controlledPollutants: string[]
  controlTargets: string
  inputUser?: string
  inputTime?: string
  updateUser?: string
  updateTime?: string
  dataStatus: number
  remarks?: string
  // 显示用的名称字段
  policyLevelName?: string
  policyTypeName?: string
  dataStatusName?: string
  applicableRegionList?: string[]
  controlledPollutantsList?: string[]
  fileList?: PolicyFile[]
}

// 仓库信息类型
export interface WarehouseInfo {
  id: string | number
  code: string
  name: string
  label: string
  value: string
  type?: string
  address?: string
  status: 'active' | 'inactive'
  createTime?: string
}

// 入库单类型
export interface EntryOrder {
  id: string | number
  entryNo: string
  source: 'manual' | 'system' | 'import'
  businessType: 'purchase' | 'return' | 'transfer'
  status: 'pending' | 'approved' | 'rejected'
  amount: number
  warehouseId?: string | number
  warehouseName?: string
  createTime: string
  updateTime?: string
  creator: string
  remark?: string
}

// 文件上传响应
export interface UploadResult {
  url: string
  filename: string
  size: number
  type: string
}

// 字典数据类型
export interface DictData {
  label: string
  value: string | number
  type?: string
  sort?: number
  remark?: string
  status?: 'active' | 'inactive'
}

// 搜索参数基础类型
export interface BaseSearchParams extends PageParams {
  keyword?: string
  startTime?: string
  endTime?: string
  status?: string
  createTimeRange?: string[]
}

// 优秀案例搜索参数
export interface ExcellentCaseSearchParams {
  currentPage: number
  pageSize: number
  caseName?: string // 案例名称
  applicableRegion?: string // 适用地区（下拉，适用地区字典）
  caseProblemTypes?: string // 案例问题类型（下拉，字典alwtlx）
  updateDate?: string[] // 更新时间（区间日期选择框），yyyy-MM-dd HH:mm:ss
  updateStartDate?: string // 更新时间（开始，区间日期选择框），yyyy-MM-dd HH:mm:ss
  updateEndDate?: string // 更新时间（结束，区间日期选择框），yyyy-MM-dd HH:mm:ss
  dataStatus?: number // 数据状态（下拉，状态下拉）
}

// 优秀案例实施期间
export interface ImplementationPeriod {
  startDate: string
  endDate: string
}

// 优秀案例文件信息
export interface CaseFile {
  fileId: string
  fileName: string
  filePath?: string
  tableName?: string
  tableId?: string
  fileType?: string
  isActive?: boolean
  url: string
}

// 优秀案例信息
export interface CaseInfo {
  caseId?: string
  caseName: string
  applicableRegion: string[]
  implementationTarget: string
  implementationPeriod: ImplementationPeriod[]
  caseProblemTypes: string[]
  applicationTarget: string
  coreTheme: string
  featuredContent: string
  caseSource: string
  policyIds: string[]
  inputUser?: string
  inputTime?: string
  updateUser?: string
  updateTime?: string
  dataStatus: number
  remarks?: string
  // 显示用的名称字段
  applicableRegionName?: string
  caseProblemTypesName?: string
  implementationPeriodName?: string
  policyIdsName?: string
  dataStatusName?: string
  fileList?: any
}

// 优秀案例列表响应数据
export interface CaseInfoListResponse {
  list: CaseInfo[]
  pagination: {
    currentPage: number
    pageSize: number
    tableProp: string
    tableOrder: string
    total: number
  }
}

// 优秀案例表单数据
export interface CaseFormData {
  info: CaseInfo
  fileList: {
    addFileIds: string[]
  }
}

// 优秀案例审批数据
export interface CaseApprovalData {
  info: {
    caseId: string
    dataStatus: number | string
  }
}

// 优秀案例删除数据
export interface CaseDeleteData {
  info: {
    caseId: string
  }
}

// ==================== 排查案例库相关类型 ====================

// 排查案例库搜索参数
export interface CheckCaseSearchParams {
  currentPage: number
  pageSize: number
  checkCaseName?: string // 案例名称
  applicableRegion?: string // 适用地区（下拉，适用地区字典）
  investigationStartDate?: string // 排查时间（开始，区间日期选择框），yyyy-MM-dd
  investigationEndDate?: string // 排查时间（结束，区间日期选择框），yyyy-MM-dd
  investigationTarget?: string // 排查对象
  source?: string // 排查来源（下拉，字典pcly）
  investigationType?: string // 排查类型（下拉，字典pclx）
  problemType?: string[] // 排查问题类型（下拉多选，字典pcwtlx）
  dataStatus?: number | string // 案例状态（下拉，状态下拉）
}

// 排查问题详情
export interface CheckProblemInfo {
  problemId?: string
  checkCaseId?: string
  problemType: string[] // 排查问题类型（下拉多选，字典pcwtlx）
  mainProblemDescription: string // 主要问题描述
  regulationClause: string // 依据法规条款
  measures: string // 处理措施
  rectificationDeadline: string // 整改时限
  rectificationResult: number | string // 整改结果（下拉，整改结果下拉）
  inputUser?: string
  inputTime?: string
  updateUser?: string
  updateTime?: string
  remarks?: string // 备注
  problemTypeName?: string // 排查问题类型名称
  rectificationResultName?: string // 整改结果名称
  rowStatus?: number
}

// 排查案例库信息
export interface CheckCaseInfo {
  checkCaseId?: string
  checkCaseName: string // 案例名称
  applicableRegion: string[] // 实施地区（下拉，适用地区字典，多选）
  investigationStartDate: string // 排查时间（开始，区间日期选择框），yyyy-MM-dd
  investigationEndDate: string // 排查时间（结束，区间日期选择框），yyyy-MM-dd
  investigationTarget: string // 排查对象
  source: string[] // 排查来源（下拉，字典pcly）
  investigationType: string[] // 排查类型（下拉，字典pclx）
  policyIds: string[] // 关联政策
  investigationMethod: string // 排查方法
  investigationScope: string // 排查范围
  caseSummary: string // 案例分析总结
  inputUser?: string
  inputTime?: string
  updateUser?: string
  updateTime?: string // 更新时间
  dataStatus: number // 数据状态（暂存-2，提交-3）
  remarks?: string // 备注
  problemList: CheckProblemInfo[] // 问题详情数组
  // 显示用的名称字段
  applicableRegionName?: string // 排查地区
  sourceName?: string // 排查来源
  investigationTypeName?: string // 排查类型
  policyIdsName?: string
  dataStatusName?: string // 案例状态
  problemTypeName?: string // 排查问题类型
  fileList?: CheckCaseFile[] // 附件
}

// 排查案例库文件信息
export interface CheckCaseFile {
  fileId: string
  fileName: string
  filePath?: string
  tableName?: string
  tableId?: string
  fileType?: string
  isActive?: boolean
  url: string
}

// 排查案例库列表响应数据
export interface CheckCaseInfoListResponse {
  list: CheckCaseInfo[]
  pagination: {
    currentPage: number
    pageSize: number
    tableProp: string
    tableOrder: string
    total: number
  }
}

// 排查案例库表单数据
export interface CheckCaseFormData {
  info: CheckCaseInfo
  fileList: {
    addFileIds: string[]
    delFileIds: string[]
  }
}

// 排查案例库审批数据
export interface CheckCaseApprovalData {
  info: {
    checkCaseId: string
    dataStatus: number // 数据状态（通过（1），不通过（-3））
  }
}

// 排查案例库删除数据
export interface CheckCaseDeleteData {
  info: {
    checkCaseId: string
  }
}

// 入库单搜索参数
export interface EntryOrderSearchParams extends BaseSearchParams {
  entryNo?: string
  source?: string
  businessType?: string
  warehouseId?: string | number
  creator?: string
  amountRange?: [number, number]
}

// ==================== 核算系数库相关类型 ====================

// 核算系数库搜索参数
export interface GuidelineInfoSearchParams {
  currentPage: number
  pageSize: number
  guidelineName?: string // 核算指南名称
  issueStartDate?: string // 发布日期（开始，区间日期选择框），yyyy-MM-dd
  issueEndDate?: string // 发布日期（结束，区间日期选择框），yyyy-MM-dd
  purpose?: string // 用途（下拉，字典hsyt）
  applicableScope?: string // 适用范围
  accountingEntity?: string // 核算对象说明
  dataStatus?: number | string // 数据状态（下拉，状态下拉）
  [key: string]: any
}

// 核算系数库文件信息
export interface GuidelineFile {
  fileId: string
  fileName: string
  filePath?: string
  tableName?: string
  tableId?: string
  fileType?: string
  isActive?: boolean
  url: string
}

// 核算系数库信息
export interface GuidelineInfo {
  guidelineId?: string
  guidelineName: string
  issuingDepartment: string
  issueDate: string
  purpose: string[]
  applicableScope: string
  accountingEntity: string
  methodology: string
  keyFormulas: string
  inputUser?: string
  inputTime?: string
  updateUser?: string
  updateTime?: string
  dataStatus: number
  remarks?: string
  // 显示用的名称字段
  purposeName?: string
  dataStatusName?: string
  fileList?: GuidelineFile[]
}

// 核算系数库列表响应数据
export interface GuidelineInfoListResponse {
  list: GuidelineInfo[]
  pagination: {
    currentPage: number
    pageSize: number
    tableProp: string
    tableOrder: string
    total: number
  }
}

// 核算系数库表单数据
export interface GuidelineFormData {
  info: GuidelineInfo
  fileList: {
    addFileIds: string[]
    delFileIds?: string[]
  }
}

// 核算系数库审批数据
export interface GuidelineApprovalData {
  info: {
    guidelineId: string
    dataStatus: number | string
  }
}

// 核算系数库删除数据
export interface GuidelineDeleteData {
  info: {
    guidelineId: string
  }
}

// ==================== 特征组分来源库相关类型 ====================

// 特征组分来源库搜索参数
export interface ComponentInfoSearchParams {
  currentPage: number
  pageSize: number
  componentName?: string // 组分名称
  componentType?: string // 组分类型（下拉，字典tzzflx一级）
  subType?: string // 子类型（下拉，字典tzzflx二级）
  isOdorousComponent?: number | null // 异味组分判定（1-是，0-否）
  sourceTypeLevel1?: string // 来源类型（一级）（下拉，字典tzlylx）
  dataStatus?: number | null // 数据状态（下拉，状态下拉）
}

// 特征组分来源库文件信息
export interface ComponentFile {
  fileId: string
  fileName: string
  filePath?: string
  tableName?: string
  tableId?: string
  fileType?: string
  isActive?: boolean
  url: string
}

// 特征组分来源库信息
export interface ComponentInfo {
  componentId?: string // 组分唯一ID
  componentName: string // 组分名称
  molecularFormula: string // 分子式
  componentType: string // 组分类型（下拉，字典tzzflx一级）
  subType: string // 子类型（下拉，字典tzzflx二级）
  isOdorousComponent: number // 异味组分判定（1-是，0-否）
  sourceTypeLevel1: string[] // 来源类型（一级）（下拉，字典tzlylx）
  sourceTypeLevel2: string // 来源类型（二级）
  concentrationRange: string // 浓度范围
  odorDescription: string // 气味特征描述
  odorThreshold: string // 嗅阈值
  odorThresholdUnit: string // 嗅阈值单位
  healthHazard: string // 健康危害
  ozoneFormationPotential: number // 臭氧生成潜势系数
  soaFormationPotential: number // 二次有机气溶胶生成潜势系数
  reference: string // 参考文献/来源
  inputUser?: string
  inputTime?: string
  updateUser?: string
  updateTime?: string
  dataStatus: number // 数据状态（暂存-2，提交-3）
  remarks?: string // 备注
  // 显示用的名称字段
  componentTypeName?: string
  subTypeName?: string
  isOdorousComponentName?: string
  sourceTypeLevel1Name?: string
  dataStatusName?: string
  fileList?: ComponentFile[]
}

// 特征组分来源库列表响应数据
export interface ComponentInfoListResponse {
  list: ComponentInfo[]
  pagination: {
    currentPage: number
    pageSize: number
    tableProp: string
    tableOrder: string
    total: number
  }
}

// 特征组分来源库表单数据
export interface ComponentFormData {
  info: ComponentInfo
  fileList: {
    addFileIds: string[]
    delFileIds: string[]
  }
}

// 特征组分来源库审批数据
export interface ComponentApprovalData {
  info: {
    checkCaseId: string // 数据id
    dataStatus: number // 数据状态（通过（1），不通过（-3））
  }
}

// 特征组分来源库删除数据
export interface ComponentDeleteData {
  info: {
    componentId: string
  }
}
