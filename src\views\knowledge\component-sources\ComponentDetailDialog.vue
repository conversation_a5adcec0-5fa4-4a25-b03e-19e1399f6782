<template>
  <el-dialog
    :model-value="visible"
    title="特征组分详情"
    width="60%"
    :before-close="handleClose"
    @update:model-value="$emit('update:visible', $event)"
  >
    <div v-if="detailData" class="component-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="组分名称">
          {{ detailData.componentName }}
        </el-descriptions-item>
        <el-descriptions-item label="分子式">
          {{ detailData.molecularFormula }}
        </el-descriptions-item>
        <el-descriptions-item label="组分类型">
          {{ detailData.componentTypeName }}
        </el-descriptions-item>
        <el-descriptions-item label="子类型">
          {{ detailData.subTypeName }}
        </el-descriptions-item>
        <el-descriptions-item label="异味组分判定">
          {{ detailData.isOdorousComponentName }}
        </el-descriptions-item>
        <el-descriptions-item label="来源类型（一级）">
          {{ detailData.sourceTypeLevel1Name }}
        </el-descriptions-item>
        <el-descriptions-item label="来源类型（二级）">
          {{ detailData.sourceTypeLevel2 || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="浓度范围">
          {{ detailData.concentrationRange || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="气味特征描述">
          {{ detailData.odorDescription || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="嗅阈值">
          {{ detailData.odorThreshold || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="嗅阈值单位">
          {{ detailData.odorThresholdUnit || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="健康危害">
          {{ detailData.healthHazard || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="臭氧生成潜势系数">
          {{ detailData.ozoneFormationPotential || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="二次有机气溶胶生成潜势系数">
          {{ detailData.soaFormationPotential || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="数据状态">
          <el-tag :type="getStatusType(detailData.dataStatus)" size="small">
            {{ detailData.dataStatusName }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ detailData.inputTime || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ detailData.updateTime || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="参考文献/来源" :span="2">
          <div class="text-content">
            {{ detailData.reference || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          <div class="text-content">
            {{ detailData.remarks || '-' }}
          </div>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 附件列表 -->
      <div v-if="detailData.fileList && detailData.fileList.length > 0" class="file-section">
        <h3>附件列表</h3>
        <el-table :data="detailData.fileList" style="width: 100%">
          <el-table-column prop="fileName" label="文件名" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button size="small" @click="previewFile(scope.row)">预览</el-button>
              <el-button size="small" type="primary" @click="downloadFile(scope.row)"
                >下载</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { ComponentInfo } from '@/api/types'
import { previewFileAuto, downloadFiles, type FileInfo } from '@/utils/fileUtils'

interface Props {
  visible: boolean
  detailData: ComponentInfo | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 获取状态类型
const getStatusType = (status: number) => {
  switch (status) {
    case 1:
      return 'success' // 正常
    case 2:
      return 'info' // 暂存
    case 3:
      return 'warning' // 待审核
    case -1:
      return 'danger' // 失效
    case -3:
      return 'danger' // 不通过
    default:
      return 'info'
  }
}

// 预览文件
const previewFile = (file: any) => {
  const fileInfo = {
    fileId: file.fileId,
    fileName: file.fileName,
    url: file.url,
    filePath: file.filePath,
    fileType: file.fileType,
  }
  previewFileAuto(fileInfo)
}

// 下载文件
const downloadFile = (file: any) => {
  const fileInfo = {
    fileId: file.fileId,
    fileName: file.fileName,
    url: file.url,
    filePath: file.filePath,
    fileType: file.fileType,
  }
  downloadFiles([fileInfo])
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.component-detail {
  padding: 20px 0;
}

.text-content {
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
}

.file-section {
  margin-top: 30px;
}

.file-section h3 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  background-color: #f8f9fa;
}

:deep(.el-descriptions__content) {
  background-color: #fff;
}
</style>
