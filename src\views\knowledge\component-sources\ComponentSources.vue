<template>
  <div class="component-info-list">
    <!-- 搜索区域 -->
    <CollapsePanel title="搜索条件" :show-toggle-btn="false" :expended="true">
      <template #panel-button>
        <el-button size="small" type="primary" @click="handleSearch">搜索</el-button>
        <el-button size="small" @click="handleReset">重置</el-button>
      </template>
      <template #panel-main>
        <!-- 搜索表单 -->
        <SearchForm ref="searchFormRef" :config="searchConfig" :model="searchParams" />
      </template>
    </CollapsePanel>
    <CollapsePanel
      :title="`共找到${pagination.total}条记录`"
      :show-toggle-btn="false"
      :expended="true"
    >
      <template #panel-button>
        <el-button type="primary" :icon="Plus" @click="handleAdd" size="small">新增</el-button>
      </template>
      <template #panel-main>
        <div class="lsit-container">
          <el-table
            :data="tableData"
            style="width: 100%"
            v-loading="loading"
            @selection-change="handleSelectionChange"
          >
            <!-- <el-table-column type="selection" width="55" /> -->
            <el-table-column prop="componentName" label="组分名称" />
            <el-table-column prop="molecularFormula" label="分子式" />
            <el-table-column prop="componentTypeName" label="组分类型" />
            <el-table-column prop="subTypeName" label="子类型" />
            <el-table-column prop="isOdorousComponentName" label="异味组分" />
            <el-table-column prop="sourceTypeLevel1Name" label="来源类型" />
            <el-table-column prop="concentrationRange" label="浓度范围" />
            <el-table-column prop="dataStatusName" label="数据状态">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.dataStatus)" size="small">
                  {{ scope.row.dataStatusName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="更新时间" />
            <el-table-column label="操作" width="300" fixed="right">
              <template #default="scope">
                <el-button size="small" @click="handleView(scope.row)">查看</el-button>
                <el-button size="small" type="primary" @click="handleEdit(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  v-if="scope.row.dataStatus === 3"
                  size="small"
                  type="success"
                  @click="handleApprove(scope.row, 1)"
                >
                  通过
                </el-button>
                <el-button
                  v-if="scope.row.dataStatus === 3"
                  size="small"
                  type="warning"
                  @click="handleApprove(scope.row, -3)"
                >
                  不通过
                </el-button>
                <el-button size="small" type="danger" @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </template>
    </CollapsePanel>
    <!-- 操作按钮区域 -->
    <!-- <div class="action-bar">
      <el-button type="primary" :icon="Plus" @click="handleAdd">新增</el-button>
    </div> -->

    <!-- 表格区域 -->
    <!-- <el-card> </el-card> -->

    <!-- 表单弹窗 -->
    <ComponentFormDialog
      v-model:visible="formDialogVisible"
      :form-data="currentFormData"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 详情弹窗 -->
    <ComponentDetailDialog v-model:visible="detailDialogVisible" :detail-data="currentDetailData" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import CollapsePanel from '@/components/CollapsePanel.vue'
import SearchForm from '@/components/SearchForm.vue'
import type { FormItem } from '@/components/SearchForm.vue'
import type { ComponentInfo, ComponentInfoSearchParams } from '@/api/types'
import { componentApi } from '@/api/knowledge'
import { fetchDictDataByTypes } from '@/utils/options'
import ComponentFormDialog from './ComponentFormDialog.vue'
import ComponentDetailDialog from './ComponentDetailDialog.vue'

const searchFormRef = ref()

// 搜索参数
const searchParams = reactive<ComponentInfoSearchParams>({
  currentPage: 1,
  pageSize: 20,
  componentName: '',
  componentType: '',
  subType: '',
  isOdorousComponent: null,
  sourceTypeLevel1: '',
  dataStatus: null,
})

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0,
})

// 表格数据
const tableData = ref<ComponentInfo[]>([])
const loading = ref(false)
const selectedRows = ref<ComponentInfo[]>([])

// 弹窗控制
const formDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEdit = ref(false)
const currentFormData = ref<ComponentInfo | null>(null)
const currentDetailData = ref<ComponentInfo | null>(null)

// 字典数据
const dictOptions = reactive<{ [key: string]: any[] }>({
  componentType: [],
  subType: [],
  sourceTypeLevel1: [],
  dataStatus: [],
})

// 搜索配置
const searchConfig = ref<FormItem[]>([
  {
    type: 'input',
    prop: 'componentName',
    formItem: { label: '组分名称' },
    attrs: {
      placeholder: '请输入组分名称',
      clearable: true,
    },
  },
  {
    type: 'select',
    prop: 'componentType',
    formItem: { label: '组分类型' },
    attrs: {
      placeholder: '请选择组分类型',
      clearable: true,
      options: [],
    },
    on: {
      change: (val: any) => {
        searchParams.subType = ''
        const arr: any[] = dictOptions.componentType.filter((item: any) => item.value === val)
        console.log('%c Line:190 🥟 arr', 'color:#33a5ff', arr[0]?.children)
        searchConfig.value[2].attrs.options = arr[0].children || []
      },
    },
  },
  {
    type: 'select',
    prop: 'subType',
    formItem: { label: '子类型' },
    attrs: {
      placeholder: '请选择子类型',
      clearable: true,
      options: [],
    },
  },
  {
    type: 'select',
    prop: 'isOdorousComponent',
    formItem: { label: '异味组分' },
    attrs: {
      placeholder: '请选择异味组分',
      clearable: true,
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
  },
  {
    type: 'select',
    prop: 'sourceTypeLevel1',
    formItem: { label: '来源类型' },
    attrs: {
      placeholder: '请选择来源类型',
      clearable: true,
      options: [],
    },
  },
  {
    type: 'select',
    prop: 'dataStatus',
    formItem: { label: '数据状态' },
    attrs: {
      placeholder: '请选择数据状态',
      clearable: true,
      options: [],
    },
  },
])

// 获取列表数据
const getComponentList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchParams,
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize,
    }
    const response = await componentApi.getComponentInfoList(params)
    tableData.value = response.list || []
    pagination.total = response.pagination?.total || 0
  } catch (error) {
    console.error('获取特征组分列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 初始化字典数据
const initDictData = async () => {
  try {
    const dictTypeMap = {
      componentType: 'tzzflx',
      sourceTypeLevel1: 'tzlylx',
    }
    const dictData = await fetchDictDataByTypes(dictTypeMap)
    // 获取状态下拉
    const statusRes = await componentApi.getDataStatusList()
    dictData.dataStatus = statusRes || []
    // 处理组分类型
    const arr = dictData.componentType.filter((item: any) => item.parentValue === 'tzzflx')
    arr.forEach((i) => {
      const childrenArr = dictData.componentType.filter((j: any) => i.value === j.parentValue)
      i.children = childrenArr
    })
    dictData.componentType = arr
    // 更新搜索配置中的选项
    searchConfig.value.forEach((item) => {
      if (item.type === 'select' && dictData[item.prop]) {
        if (!item.attrs) item.attrs = {}
        item.attrs.options = dictData[item.prop]
      }
    })
    Object.assign(dictOptions, dictData)
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  const formData = searchFormRef.value?.getFormData()
  Object.assign(searchParams, formData)
  pagination.currentPage = 1
  getComponentList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchParams, {
    currentPage: 1,
    pageSize: 20,
    componentName: '',
    componentType: '',
    subType: '',
    isOdorousComponent: null,
    sourceTypeLevel1: '',
    dataStatus: null,
  })
  searchFormRef.value?.resetFields()
  pagination.currentPage = 1
  getComponentList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  getComponentList()
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  getComponentList()
}

// 选择处理
const handleSelectionChange = (selection: ComponentInfo[]) => {
  selectedRows.value = selection
}

// 新增
const handleAdd = () => {
  currentFormData.value = null
  isEdit.value = false
  formDialogVisible.value = true
}

// 编辑
const handleEdit = (row: ComponentInfo) => {
  currentFormData.value = { ...row }
  isEdit.value = true
  formDialogVisible.value = true
}

// 查看详情
const handleView = (row: ComponentInfo) => {
  currentDetailData.value = row
  detailDialogVisible.value = true
}

// 删除
const handleDelete = async (row: ComponentInfo) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await componentApi.deleteComponentInfo({
      info: { componentId: row.componentId! },
    })

    if (response.code === 200) {
      ElMessage.success('删除成功')
      getComponentList()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 审核
const handleApprove = async (row: ComponentInfo, status: number) => {
  try {
    const statusText = status === 1 ? '通过' : '不通过'
    await ElMessageBox.confirm(`确定要${statusText}这条记录吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await componentApi.approvedComponentInfo({
      info: {
        checkCaseId: row.componentId!,
        dataStatus: status,
      },
    })

    if (response.code === 200) {
      ElMessage.success(`${statusText}成功`)
      getComponentList()
    } else {
      ElMessage.error(response.msg || `${statusText}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核失败:', error)
      ElMessage.error('审核失败')
    }
  }
}

// 表单提交成功
const handleFormSuccess = () => {
  formDialogVisible.value = false
  getComponentList()
}

// 获取状态类型
const getStatusType = (status: number) => {
  switch (status) {
    case 1:
      return 'success' // 正常
    case 2:
      return 'info' // 暂存
    case 3:
      return 'warning' // 待审核
    case -1:
      return 'danger' // 失效
    case -3:
      return 'danger' // 不通过
    default:
      return 'info'
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getComponentList()
  initDictData()
})
</script>

<style scoped>
.component-info-list {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 40px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.page-header p {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}

/* .pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
} */

/* :deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9fa;
} */

:deep(.el-button) {
  border-radius: 6px;
}

:deep(.el-tag) {
  border-radius: 4px;
}
.pagination-container {
  padding: 20px 0;
  display: flex;
  justify-content: center;
}
.list-container {
  margin-top: 20px;
}
</style>
