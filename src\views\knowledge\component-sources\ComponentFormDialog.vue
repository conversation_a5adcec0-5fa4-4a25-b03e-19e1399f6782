<template>
  <el-dialog
    :model-value="visible"
    :title="isEdit ? '编辑特征组分' : '新增特征组分'"
    width="60%"
    :before-close="handleClose"
    @update:model-value="$emit('update:visible', $event)"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      class="component-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="组分名称" prop="componentName">
            <el-input v-model="formData.componentName" placeholder="请输入组分名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分子式" prop="molecularFormula">
            <el-input v-model="formData.molecularFormula" placeholder="请输入分子式" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="组分类型" prop="componentType">
            <el-select
              v-model="formData.componentType"
              placeholder="请选择组分类型"
              clearable
              style="width: 100%"
              @change="handleComponentTypeChange"
            >
              <el-option
                v-for="item in componentTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="子类型" prop="subType">
            <el-select
              v-model="formData.subType"
              placeholder="请选择子类型"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in subTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="异味组分判定" prop="isOdorousComponent">
            <el-radio-group v-model="formData.isOdorousComponent">
              <el-radio :value="1">是</el-radio>
              <el-radio :value="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="来源类型（一级）" prop="sourceTypeLevel1">
            <el-select
              v-model="formData.sourceTypeLevel1"
              placeholder="请选择来源类型"
              multiple
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in sourceTypeLevel1Options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="来源类型（二级）" prop="sourceTypeLevel2">
            <el-input
              v-model="formData.sourceTypeLevel2"
              placeholder="请输入来源类型（二级）"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="浓度范围" prop="concentrationRange">
            <el-input
              v-model="formData.concentrationRange"
              placeholder="请输入浓度范围"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="气味特征描述" prop="odorDescription">
            <el-input
              v-model="formData.odorDescription"
              placeholder="请输入气味特征描述"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="嗅阈值" prop="odorThreshold">
            <el-input v-model="formData.odorThreshold" placeholder="请输入嗅阈值" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="嗅阈值单位" prop="odorThresholdUnit">
            <el-input
              v-model="formData.odorThresholdUnit"
              placeholder="请输入嗅阈值单位"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="健康危害" prop="healthHazard">
            <el-input v-model="formData.healthHazard" placeholder="请输入健康危害" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="臭氧生成潜势系数" prop="ozoneFormationPotential">
            <el-input-number
              v-model="formData.ozoneFormationPotential"
              placeholder="请输入臭氧生成潜势系数"
              style="width: 100%"
              :precision="2"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="二次有机气溶胶生成潜势系数" prop="soaFormationPotential">
            <el-input-number
              v-model="formData.soaFormationPotential"
              placeholder="请输入二次有机气溶胶生成潜势系数"
              style="width: 100%"
              :precision="2"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="参考文献/来源" prop="reference">
            <el-input
              v-model="formData.reference"
              type="textarea"
              :rows="3"
              placeholder="请输入参考文献/来源"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="附件">
            <el-upload
              ref="uploadRef"
              :file-list="fileList"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :before-upload="beforeUpload"
              :auto-upload="false"
              multiple
              drag
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <template #tip>
                <div class="el-upload__tip">
                  支持 jpg/png/gif/pdf/doc/docx/xls/xlsx 文件，且不超过 10MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="info" @click="handleSaveDraft">保存草稿</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElForm } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { ComponentInfo } from '@/api/types'
import { componentApi } from '@/api/knowledge'
import { fetchDictDataByTypes } from '@/utils/options'

interface Props {
  visible: boolean
  formData: ComponentInfo | null
  isEdit: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<InstanceType<typeof ElForm>>()
const uploadRef = ref()

// 表单数据
const formData = reactive<ComponentInfo>({
  componentId: '',
  componentName: '',
  molecularFormula: '',
  componentType: '',
  subType: '',
  isOdorousComponent: 0,
  sourceTypeLevel1: [],
  sourceTypeLevel2: '',
  concentrationRange: '',
  odorDescription: '',
  odorThreshold: '',
  odorThresholdUnit: '',
  healthHazard: '',
  ozoneFormationPotential: 0,
  soaFormationPotential: 0,
  reference: '',
  dataStatus: 2,
  remarks: '',
})

// 文件列表
const fileList = ref<any[]>([])
const addFileIds = ref<string[]>([])
const delFileIds = ref<string[]>([])

// 字典选项
const componentTypeOptions = ref<any[]>([])
const subTypeOptions = ref<any[]>([])
const sourceTypeLevel1Options = ref<any[]>([])

// 表单验证规则
const formRules = {
  componentName: [{ required: true, message: '请输入组分名称', trigger: 'blur' }],
  molecularFormula: [{ required: true, message: '请输入分子式', trigger: 'blur' }],
  componentType: [{ required: true, message: '请选择组分类型', trigger: 'change' }],
  subType: [{ required: true, message: '请选择子类型', trigger: 'change' }],
  isOdorousComponent: [{ required: true, message: '请选择异味组分判定', trigger: 'change' }],
  sourceTypeLevel1: [{ required: true, message: '请选择来源类型', trigger: 'change' }],
}

// 监听表单数据变化
watch(
  () => props.formData,
  (newData) => {
    if (newData) {
      Object.assign(formData, newData)
      // 处理文件列表
      if (newData.fileList) {
        fileList.value = newData.fileList.map((file) => ({
          name: file.fileName,
          url: file.url,
          fileId: file.fileId,
        }))
      }
    } else {
      // resetForm()
    }
  },
  { immediate: true },
)

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    componentId: '',
    componentName: '',
    molecularFormula: '',
    componentType: '',
    subType: '',
    isOdorousComponent: 0,
    sourceTypeLevel1: [],
    sourceTypeLevel2: '',
    concentrationRange: '',
    odorDescription: '',
    odorThreshold: '',
    odorThresholdUnit: '',
    healthHazard: '',
    ozoneFormationPotential: 0,
    soaFormationPotential: 0,
    reference: '',
    dataStatus: 2,
    remarks: '',
  })
  fileList.value = []
  addFileIds.value = []
  delFileIds.value = []
  formRef.value?.clearValidate()
}

// 组分类型变化处理
const handleComponentTypeChange = async (value: string) => {
  formData.subType = ''
  if (value) {
    try {
      // 获取子类型数据（二级字典）
      const dictData = await fetchDictDataByTypes({
        subType: 'tzzflx',
      })
      // 过滤出对应的二级数据
      subTypeOptions.value = dictData.subType.filter((item: any) => item.parentValue === value)
    } catch (error) {
      console.error('获取子类型数据失败:', error)
    }
  } else {
    subTypeOptions.value = []
  }
}

// 文件上传处理
const handleFileChange = (file: any, fileList: any[]) => {
  // 这里可以处理文件上传逻辑
  console.log('文件变化:', file, fileList)
}

const handleFileRemove = (file: any, fileList: any[]) => {
  if (file.fileId) {
    delFileIds.value.push(file.fileId)
  }
}

const beforeUpload = (file: any) => {
  const isValidType = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ].includes(file.type)

  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('上传文件只能是 JPG/PNG/GIF/PDF/DOC/DOCX/XLS/XLSX 格式!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  return false // 阻止自动上传
}

// 保存草稿
const handleSaveDraft = async () => {
  try {
    await formRef.value?.validate()
    await submitForm(2) // 草稿状态
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    await submitForm(3) // 提交状态
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 提交表单
const submitForm = async (dataStatus: number) => {
  try {
    const submitData = {
      info: {
        ...formData,
        dataStatus,
      },
      fileList: {
        addFileIds: addFileIds.value,
        delFileIds: delFileIds.value,
      },
    }

    let response
    if (props.isEdit) {
      response = await componentApi.updateComponentInfo(submitData)
    } else {
      response = await componentApi.saveComponentInfo(submitData)
    }

    if (response.code === 200) {
      ElMessage.success(`${props.isEdit ? '更新' : '新增'}${dataStatus === 2 ? '草稿' : ''}成功`)
      emit('success')
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

// 初始化字典数据
const initDictData = async () => {
  try {
    const dictTypeMap = {
      componentType: 'tzzflx',
      sourceTypeLevel1: 'tzlylx',
    }
    const dictData = await fetchDictDataByTypes(dictTypeMap)
    componentTypeOptions.value = dictData.componentType || []
    sourceTypeLevel1Options.value = dictData.sourceTypeLevel1 || []
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

onMounted(() => {
  initDictData()
})
</script>

<style scoped>
.component-form {
  max-height: 60vh;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 600;
}

:deep(.el-upload-dragger) {
  border-radius: 8px;
}
</style>
