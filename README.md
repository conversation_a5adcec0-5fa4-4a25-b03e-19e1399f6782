# vite2

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
pnpm install
```

### Compile and Hot-Reload for Development

```sh
pnpm run dev
```

### Type-Check, Compile and Minify for Production

```sh
pnpm run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
pnpm run lint
```

现在我有以下需求：

1. 我要开发"特征组分来源库"菜单，该菜单是知识库管理的二级菜单；
2. 主要做增删改查功能，接下来我会把接口全部写出来：
   2-1. 获取 列表接口：/ComponentInfo/ComponentInfoList
   入参是：
   {
   "currentPage": 1,
   "pageSize": 2,
   "componentName": "", //组分名称
   "componentType": "", //组分类型（下拉，字典tzzflx一级）
   "subType": "", //子类型（下拉，字典tzzflx二级）
   "isOdorousComponent": null, //异味组分判定（1-是，0-否）
   "sourceTypeLevel1": "", //来源类型（一级）（下拉，字典tzlylx）
   "dataStatus": null //数据状态（下拉，状态下拉）
   }
   出参是：
   {
   "code": 200,
   "msg": "success",
   "data": {
   "list": [
   {
   "componentId": "TZZF20250823001",
   "componentName": "111",
   "molecularFormula": "222",
   "componentType": "2",
   "subType": "203",
   "isOdorousComponent": 0,
   "sourceTypeLevel1": [
   "2"
   ],
   "sourceTypeLevel2": "444",
   "concentrationRange": "555",
   "odorDescription": "666",
   "odorThreshold": "777",
   "odorThresholdUnit": "888",
   "healthHazard": "999",
   "ozoneFormationPotential": 22,
   "soaFormationPotential": 33,
   "reference": "44",
   "inputUser": null,
   "inputTime": "2025-09-16 16:59:03",
   "updateUser": null,
   "updateTime": "2025-09-16 16:59:04",
   "dataStatus": 1,
   "remarks": "ffff",
   "componentTypeName": "VOCs",
   "subTypeName": "炔烃",
   "isOdorousComponentName": "否",
   "sourceTypeLevel1Name": "移动源",
   "dataStatusName": "正常",
   "fileList": [
   {
   "fileId": "4503e53b-7d55-49f3-bd71-cb381632b365",
   "fileName": "yxkj.png",
   "filePath": null,
   "tableName": null,
   "tableId": null,
   "fileType": null,
   "isActive": null,
   "url": "File/getFile?fileName=1c7f9bc3-7865-4c85-b053-b36580b5ce73.png&type=componentInfo"
   }
   ]
   }
   ],
   "pagination": {
   "currentPage": 1,
   "pageSize": 2,
   "tableProp": "",
   "tableOrder": "",
   "total": 1
   }
   }
   }

   2-2. 新增接口：/ComponentInfo/saveComponentInfo
   入参是：
   {
   "info": {
   "componentId": "TZZF20250917094946", //组分唯一ID
   "componentName": "111ddd", //组分名称
   "molecularFormula": "222", //分子式
   "componentType": "2", //组分类型（下拉，字典tzzflx一级）
   "subType": "203", //子类型（下拉，字典tzzflx二级）
   "isOdorousComponent": 0, //异味组分判定（1-是，0-否）
   "sourceTypeLevel1": [
   "2"
   ], //来源类型（一级）（下拉，字典tzlylx）
   "sourceTypeLevel2": "444", //来源类型（二级）
   "concentrationRange": "555", //浓度范围
   "odorDescription": "666", //气味特征描述
   "odorThreshold": "777", //嗅阈值
   "odorThresholdUnit": "888", //嗅阈值单位
   "healthHazard": "999", //健康危害
   "ozoneFormationPotential": 22, //臭氧生成潜势系数
   "soaFormationPotential": 33, //二次有机气溶胶生成潜势系数
   "reference": "44", //参考文献/来源
   "dataStatus": 2, //数据状态（暂存-2，提交-3）
   "remarks": "33333" //备注
   },
   "fileList": {
   "addFileIds": [
   "",
   ""
   ], //新增的文件id（文件上传接口type=componentInfo返回值）
   "delFileIds": [
   "",
   ""
   ] //删除的文件id
   } //附件
   }

   2-3. 编辑接口：/ComponentInfo/updateComponentInfo，入参和新增一样
   2-4. 删除接口：/ComponentInfo/deleteComponentInfo
   入参是：
   {
   "info": {
   "componentId": "TZZF20250917094946"
   }
   }
   2-5. 获取状态下拉接口：/ComponentInfo/getDataStatusList
   {
   "code": 200,
   "msg": "success",
   "data": [
   {
   "value": 2,
   "label": "暂存",
   "minLabel": null,
   "typeName": null,
   "parentValue": null,
   "unit": null,
   "remark": null
   },
   {
   "value": 3,
   "label": "待审核",
   "minLabel": null,
   "typeName": null,
   "parentValue": null,
   "unit": null,
   "remark": null
   },
   {
   "value": 1,
   "label": "正常",
   "minLabel": null,
   "typeName": null,
   "parentValue": null,
   "unit": null,
   "remark": null
   },
   {
   "value": -1,
   "label": "失效",
   "minLabel": null,
   "typeName": null,
   "parentValue": null,
   "unit": null,
   "remark": null
   }
   ]
   }
   2-6. 审核接口：/ComponentInfo/approvedComponentInfo
   入参：{
   "info": {
   "checkCaseId": "PC20250910134596", //数据id
   "dataStatus": 1 //数据状态（通过（1），不通过（-3））
   }
   }
   2-7.获取用途的字典查询接口：使用全局封装的方法 fetchDictDataByTypes
   "componentType": "2", //组分类型（下拉，字典tzzflx一级）
   "subType": "203", //子类型（下拉，字典tzzflx二级）
   "sourceTypeLevel1": [ "2" ], //来源类型（一级）（下拉，字典tzlylx）

   2-8.获取地区数据使用全局的 fetchDictDataByTypes

3. 保存为草稿的时候dataStatus传2，提交的时候传3。
4. 解决所有TS报错。
5. 删除项目里面多余的逻辑部分。
6. 接口写在src/api/knowledge.ts 文件夹里面
7. 样式使用的是 lang = "scss"
8.
