import { createRouter, createWebHashHistory } from 'vue-router'
import Layout from '@/layout/index.vue'
// import Home from '@/views/Home.vue'

// 知识库管理
// import PolicyInfoList from '@/views/knowledge/policy/PolicyInfoList.vue'
import ExcellentCases from '@/views/knowledge/excellent/ExcellentCases.vue'
import ReductionGuide from '@/views/knowledge/ReductionGuide.vue'
// import ComponentSources from '@/views/knowledge/component-sources/ComponentSources.vue'
// import GuidelineInfo from '@/views/knowledge/guideline/GuidelineInfo.vue'

// 空气质量数据可视化
import SpatialDistribution from '@/views/air-quality/SpatialDistribution.vue'
import TimeSeries from '@/views/air-quality/TimeSeries.vue'
import RegionalRanking from '@/views/air-quality/RegionalRanking.vue'
import ComparisonAnalysis from '@/views/air-quality/ComparisonAnalysis.vue'

// 污染源可视化
import Industrial from '@/views/pollution-source/Industrial.vue'
import EnterpriseProfile from '@/views/pollution-source/EnterpriseProfile.vue'
import PointStrategy from '@/views/pollution-source/PointStrategy.vue'

// 污染成因分析
import HighValueFactors from '@/views/pollution-analysis/HighValueFactors.vue'
import HighValuePeriods from '@/views/pollution-analysis/HighValuePeriods.vue'
import HighValueRegions from '@/views/pollution-analysis/HighValueRegions.vue'
import MeteorologicalCorrelation from '@/views/pollution-analysis/MeteorologicalCorrelation.vue'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: Layout,
      children: [
        {
          path: '',
          name: 'Home',
          component: () => import('@/views/Home.vue'),
        },
        // 知识库管理
        // PolicyInfoList
        {
          path: '/knowledge/policy-list',
          name: 'PolicyInfoList',
          component: () => import('@/views/knowledge/policy/PolicyInfoList.vue'),
        },
        {
          path: '/knowledge/excellent-cases',
          name: 'ExcellentCases',
          component: () => import('@/views/knowledge/excellent/ExcellentCases.vue'),
        },
        {
          path: '/knowledge/guideline-info',
          name: 'GuidelineInfo',
          component: () => import('@/views/knowledge/guideline/GuidelineInfo.vue'),
        },
        {
          path: '/knowledge/check-case-list',
          name: 'CheckCaseList',
          component: () => import('@/views/knowledge/checkcase/CheckCaseList.vue'),
        },
        {
          path: '/knowledge/component-sources',
          name: 'ComponentSources',
          component: () => import('@/views/knowledge/component-sources/ComponentSources.vue'),
        },
        {
          path: '/knowledge/excellent-cases/industrial',
          name: 'ExcellentCasesIndustrial',
          component: ExcellentCases,
        },
        {
          path: '/knowledge/excellent-cases/urban',
          name: 'ExcellentCasesUrban',
          component: ExcellentCases,
        },
        {
          path: '/knowledge/excellent-cases/rural',
          name: 'ExcellentCasesRural',
          component: ExcellentCases,
        },
        {
          path: '/knowledge/reduction-guide',
          name: 'ReductionGuide',
          component: ReductionGuide,
        },

        // 空气质量数据可视化
        {
          path: '/air-quality/spatial-distribution',
          name: 'SpatialDistribution',
          component: SpatialDistribution,
        },
        {
          path: '/air-quality/time-series',
          name: 'TimeSeries',
          component: TimeSeries,
        },
        {
          path: '/air-quality/regional-ranking',
          name: 'RegionalRanking',
          component: RegionalRanking,
        },
        {
          path: '/air-quality/comparison-analysis',
          name: 'ComparisonAnalysis',
          component: ComparisonAnalysis,
        },
        // 污染源可视化
        {
          path: '/pollution-source/industrial',
          name: 'Industrial',
          component: Industrial,
        },
        {
          path: '/pollution-source/industrial/steel',
          name: 'IndustrialSteel',
          component: Industrial,
        },
        {
          path: '/pollution-source/industrial/chemical',
          name: 'IndustrialChemical',
          component: Industrial,
        },
        {
          path: '/pollution-source/industrial/power',
          name: 'IndustrialPower',
          component: Industrial,
        },
        {
          path: '/pollution-source/enterprise-profile',
          name: 'EnterpriseProfile',
          component: EnterpriseProfile,
        },
        {
          path: '/pollution-source/point-strategy',
          name: 'PointStrategy',
          component: PointStrategy,
        },
        // 污染成因分析
        {
          path: '/pollution-analysis/high-value-factors',
          name: 'HighValueFactors',
          component: HighValueFactors,
        },
        {
          path: '/pollution-analysis/high-value-periods',
          name: 'HighValuePeriods',
          component: HighValuePeriods,
        },
        {
          path: '/pollution-analysis/high-value-regions',
          name: 'HighValueRegions',
          component: HighValueRegions,
        },
        {
          path: '/pollution-analysis/meteorological-correlation',
          name: 'MeteorologicalCorrelation',
          component: MeteorologicalCorrelation,
        },
      ],
    },
  ],
})

export default router
