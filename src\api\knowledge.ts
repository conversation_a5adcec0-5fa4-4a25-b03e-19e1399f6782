import { get, post, put, del } from '@/utils/request'
import type {
  ApiResponse,
  // ExcellentCase,
  PolicyInfo,
  ExcellentCaseSearchParams,
  CaseFormData,
  CaseApprovalData,
  CaseDeleteData,
  // PolicyInfoSearchParams,
  // PageResult,
  // DictData,
  GuidelineInfoSearchParams,
  GuidelineFormData,
  GuidelineApprovalData,
  GuidelineDeleteData,
  // GuidelineInfoListResponse,
  CheckCaseSearchParams,
  CheckCaseFormData,
  CheckCaseApprovalData,
  CheckCaseDeleteData,
  CheckCaseInfoListResponse,
  ComponentInfoSearchParams,
  ComponentFormData,
  ComponentApprovalData,
  ComponentDeleteData,
  ComponentInfoListResponse,
} from './types'
export interface PolicyInfoSearchParams {
  currentPage: number
  pageSize: number
  policyName: string //政策名称（文本框）
  publishStartDate: string //发布日期（开始，区间日期选择框），yyyy-MM-dd
  publishEndDate: string //发布日期（结束，区间日期选择框），yyyy-MM-dd
  policyLevel: string //政策层级（下拉，字典zccj）
  applicableRegion: string //适用地区（下拉，适用地区字典）
  policyType: string //政策类型（下拉，字典zclx）
  policyNumber: string //政策编号（文本框）
  controlledPollutants: string //涉及污染物（下拉，字典sjwrw）
  dataStatus: string //数据状态（下拉，状态下拉）
}

export interface PolicyFormData {
  info: PolicyInfo
  fileList: {
    addFileIds: string[]
  }
}
// ==================== 政策相关API ====================
export const policyApi = {
  // 获取政策信息列表
  getPolicyInfoList: (params: PolicyInfoSearchParams): Promise<any> => {
    return post('/PolicyInfo/PolicyInfoList', params)
  },

  // 保存政策信息
  savePolicyInfo: (data: PolicyFormData) => {
    return post('/PolicyInfo/savePolicyInfo', data)
  },

  // // 更新政策信息
  updatePolicyInfo: (data: PolicyFormData): Promise<ApiResponse> => {
    return post('/PolicyInfo/updatePolicyInfo', data)
  },

  // 删除政策
  deletePolicyInfo: (data: { info: { policyId: string } }) => {
    return post(`/PolicyInfo/deletePolicyInfo`, data)
  },
  // 获取状态
  getDataStatusList: (): Promise<any[]> => {
    return get('/PolicyInfo/getDataStatusList')
  },
  // 审批
  approvedPolicyInfo: (data: { info: { policyId: string; dataStatus: number | string } }) => {
    return post(`/PolicyInfo/approvedPolicyInfo`, data)
  },
}

// ==================== 优秀管控案例库 ====================
export const caseApi = {
  // 1. 获取状态下拉接口
  getDataStatusList: () => {
    return get('/CaseInfo/getDataStatusList')
  },

  // 2. 获取关联政策下拉接口
  getPolicyDictList: () => {
    return get('/CaseInfo/getPolicyDictList')
  },

  // 3. 获取优秀案例列表
  getCaseInfoList: (params: ExcellentCaseSearchParams) => {
    return post('/CaseInfo/CaseInfoList', params)
  },

  // 4. 新增优秀案例
  saveCaseInfo: (data: CaseFormData): Promise<ApiResponse> => {
    return post('/CaseInfo/saveCaseInfo', data)
  },

  // 5. 更新优秀案例 (注意：您提到的第5点接口路径似乎有误，我使用了updateCaseInfo)
  updateCaseInfo: (data: CaseFormData): Promise<ApiResponse> => {
    return post('/CaseInfo/updateCaseInfo', data)
  },

  // 6. 审批优秀案例
  approvedCaseInfo: (data: CaseApprovalData): Promise<ApiResponse> => {
    return post('/CaseInfo/approvedCaseInfo', data)
  },

  // 7. 删除优秀案例
  deleteCaseInfo: (data: CaseDeleteData): Promise<ApiResponse> => {
    return post('/CaseInfo/deleteCaseInfo', data)
  },
}

// ==================== 核算系数库相关API ====================
export const guidelineApi = {
  // 1. 获取核算系数库列表
  getGuidelineInfoList: (params: GuidelineInfoSearchParams) => {
    return post('/GuidelineInfo/GuidelineInfoList', params)
  },

  // 2. 新增核算系数库
  saveGuidelineInfo: (data: GuidelineFormData): Promise<ApiResponse> => {
    return post('/GuidelineInfo/saveGuidelineInfo', data)
  },

  // 3. 编辑核算系数库
  updateGuidelineInfo: (data: GuidelineFormData): Promise<ApiResponse> => {
    return post('/GuidelineInfo/updateGuidelineInfo', data)
  },

  // 4. 删除核算系数库
  deleteGuidelineInfo: (data: GuidelineDeleteData): Promise<ApiResponse> => {
    return post('/GuidelineInfo/deleteGuidelineInfo', data)
  },

  // 5. 获取状态下拉
  getDataStatusList: () => {
    return get('/GuidelineInfo/getDataStatusList')
  },

  // 6. 审核接口
  approvedGuidelineInfo: (data: GuidelineApprovalData): Promise<ApiResponse> => {
    return post('/GuidelineInfo/approvedGuidelineInfo', data)
  },
}

// ==================== 排查案例库相关API ====================
export const checkCaseApi = {
  // 1. 获取排查案例库列表
  getCheckCaseInfoList: (params: CheckCaseSearchParams): Promise<CheckCaseInfoListResponse> => {
    return post('/CheckCaseInfo/CheckCaseInfoList', params)
  },

  // 2. 新增排查案例库
  saveCheckCaseInfo: (data: CheckCaseFormData): Promise<ApiResponse> => {
    return post('/CheckCaseInfo/saveCheckCaseInfo', data)
  },

  // 3. 编辑排查案例库
  updateCheckCaseInfo: (data: CheckCaseFormData): Promise<ApiResponse> => {
    return post('/CheckCaseInfo/updateCheckCaseInfo', data)
  },

  // 4. 删除排查案例库
  deleteCheckCaseInfo: (data: CheckCaseDeleteData): Promise<ApiResponse> => {
    return post('/CheckCaseInfo/deleteCheckCaseInfo', data)
  },

  // 5. 获取状态下拉
  getDataStatusList: () => {
    return get('/CheckCaseInfo/getDataStatusList')
  },

  // 6. 审核排查案例库
  approvedCheckCaseInfo: (data: CheckCaseApprovalData): Promise<ApiResponse> => {
    return post('/CheckCaseInfo/approvedCheckCaseInfo', data)
  },
}

// ==================== 特征组分来源库相关API ====================
export const componentApi = {
  // 1. 获取特征组分来源库列表
  getComponentInfoList: (params: ComponentInfoSearchParams): Promise<ComponentInfoListResponse> => {
    return post('/ComponentInfo/ComponentInfoList', params)
  },

  // 2. 新增特征组分来源库
  saveComponentInfo: (data: ComponentFormData): Promise<ApiResponse> => {
    return post('/ComponentInfo/saveComponentInfo', data)
  },

  // 3. 编辑特征组分来源库
  updateComponentInfo: (data: ComponentFormData): Promise<ApiResponse> => {
    return post('/ComponentInfo/updateComponentInfo', data)
  },

  // 4. 删除特征组分来源库
  deleteComponentInfo: (data: ComponentDeleteData): Promise<ApiResponse> => {
    return post('/ComponentInfo/deleteComponentInfo', data)
  },

  // 5. 获取状态下拉
  getDataStatusList: () => {
    return get('/ComponentInfo/getDataStatusList')
  },

  // 6. 审核特征组分来源库
  approvedComponentInfo: (data: ComponentApprovalData): Promise<ApiResponse> => {
    return post('/ComponentInfo/approvedComponentInfo', data)
  },
}
